import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsUUID } from 'class-validator';

export class NegotiationStatisticsQueryDto {
  @ApiProperty({
    description: 'Start date for filtering negotiations (ISO 8601 format)',
    example: '2024-01-01T00:00:00.000Z',
    required: true,
  })
  @IsDateString({}, { message: 'startDate must be a valid ISO 8601 date string' })
  @IsNotEmpty({ message: 'startDate is required' })
  readonly startDate: string;

  @ApiProperty({
    description: 'End date for filtering negotiations (ISO 8601 format)',
    example: '2024-12-31T23:59:59.999Z',
    required: true,
  })
  @IsDateString({}, { message: 'endDate must be a valid ISO 8601 date string' })
  @IsNotEmpty({ message: 'endDate is required' })
  readonly endDate: string;

  @ApiProperty({
    description: 'Portfolio ID to filter negotiations',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
  })
  @IsUUID('4', { message: 'portfolioId must be a valid UUID' })
  @IsNotEmpty({ message: 'portfolioId is required' })
  readonly portfolioId: string;

  constructor(startDate: string, endDate: string, portfolioId: string) {
    this.startDate = startDate;
    this.endDate = endDate;
    this.portfolioId = portfolioId;
  }
}

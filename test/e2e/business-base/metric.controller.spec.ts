import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { getAuthCredentials } from 'test/helpers/authenticated-user';
import { GroupByDate, PortfolioItemStatus } from '@common/enums';

describe('Business Base Metrics controller (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    app = global.__NEST_APP__;
    const { accessToken } = await getAuthCredentials(app, '<EMAIL>', 'password123');
    authToken = accessToken;
  });

  describe('GET /v1/business-base/metrics/portfolio/created', () => {
    it('should return portfolio created metrics for the given date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/created')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/created')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .expect(401);
    });
  });

  describe('GET /v1/business-base/metrics/portfolio/items/created', () => {
    it('should return portfolio items created metrics for the given date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/created')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/created')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .expect(401);
    });
  });

  describe('GET /v1/business-base/metrics/portfolio/items/with-interaction', () => {
    it('should return portfolio items with interaction count for the given date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/with-interaction')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupByDate: GroupByDate.DAY,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/with-interaction')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupByDate: GroupByDate.DAY,
        })
        .expect(401);
    });
  });

  describe('GET /v1/business-base/metrics/portfolio/items/ai-only-interaction', () => {
    it('should return portfolio items with only AI interaction count for the given date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/ai-only-interaction')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/ai-only-interaction')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .expect(401);
    });
  });

  describe('GET /v1/business-base/metrics/portfolio/items/grouped-by-date', () => {
    it('should return portfolio items count by date filtered by status', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/grouped-by-date')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupByDate: GroupByDate.DAY,
          currentStatus: PortfolioItemStatus.IN_PROGRESS,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/grouped-by-date')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupByDate: GroupByDate.DAY,
          currentStatus: PortfolioItemStatus.IN_PROGRESS,
        })
        .expect(401);
    });
  });

  describe('GET /v1/business-base/metrics/portfolio/deal-value', () => {
    it('should return total deal value metrics without date filtering', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/deal-value')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalDealValue).toBeDefined();
      expect(typeof response.body.data.totalDealValue).toBe('string');
      expect(response.body.data.startDate).toBeUndefined();
      expect(response.body.data.endDate).toBeUndefined();
    });

    it('should return total deal value metrics with date filtering', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/deal-value')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalDealValue).toBeDefined();
      expect(typeof response.body.data.totalDealValue).toBe('string');
      expect(response.body.data.startDate).toBe(startDate.toISOString());
      expect(response.body.data.endDate).toBe(endDate.toISOString());
    });

    it('should return total deal value metrics with only start date', async () => {
      const startDate = new Date('2024-01-01');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/deal-value')
        .query({
          startDate: startDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalDealValue).toBeDefined();
      expect(typeof response.body.data.totalDealValue).toBe('string');
      expect(response.body.data.startDate).toBe(startDate.toISOString());
      expect(response.body.data.endDate).toBeUndefined();
    });

    it('should return total deal value metrics with only end date', async () => {
      const endDate = new Date('2024-12-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/deal-value')
        .query({
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalDealValue).toBeDefined();
      expect(typeof response.body.data.totalDealValue).toBe('string');
      expect(response.body.data.startDate).toBeUndefined();
      expect(response.body.data.endDate).toBe(endDate.toISOString());
    });

    it('should return 401 when no auth token is provided', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/deal-value')
        .expect(401);
    });

    it('should handle zero deal value correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/deal-value')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalDealValue).toBeDefined();
      expect(typeof response.body.data.totalDealValue).toBe('string');
      expect(response.body.data.totalDealValue).toMatch(/^R\$\s[\d.,]+$/);
    });
  });

  describe('GET /v1/business-base/metrics/customer/average-ticket', () => {
    it('should return customer average ticket across all portfolios without date filtering', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/customer/average-ticket')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.averageTicket).toBeDefined();
      expect(typeof response.body.data.averageTicket).toBe('string');
      expect(response.body.data.startDate).toBeUndefined();
      expect(response.body.data.endDate).toBeUndefined();
    });

    it('should return customer average ticket with date filtering', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/customer/average-ticket')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.averageTicket).toBeDefined();
      expect(typeof response.body.data.averageTicket).toBe('string');
      expect(response.body.data.startDate).toBe(startDate.toISOString());
      expect(response.body.data.endDate).toBe(endDate.toISOString());
    });

    it('should return 401 when no auth token is provided', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/customer/average-ticket')
        .expect(401);
    });

    it('should handle zero successful items correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/customer/average-ticket')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.averageTicket).toBeDefined();
      expect(typeof response.body.data.averageTicket).toBe('string');
      // Should be formatted as currency even if 0
      expect(response.body.data.averageTicket).toMatch(/R\$\s[\d.,]+/);
    });
  });

  describe('GET /v1/business-base/metrics/negotiations/:portfolioId', () => {
    it('should return negotiation statistics for a valid portfolio (even with zero counts)', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      const portfolioId = '30b0e1fb-64c7-4519-93aa-4eb5dd33087e'; // Using portfolio ID from seed data

      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/metrics/negotiations/${portfolioId}`)
        .query({
          startDate: startDate,
          endDate: endDate,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.portfolios).toBeDefined();
      expect(Array.isArray(response.body.data.portfolios)).toBe(true);
      expect(response.body.data.portfolios).toHaveLength(1);
      expect(response.body.data.portfolios[0].portfolioId).toBe(portfolioId);
      expect(typeof response.body.data.portfolios[0].activeNegotiations).toBe('number');
      expect(typeof response.body.data.portfolios[0].inProgressNegotiations).toBe('number');
      expect(typeof response.body.data.portfolios[0].totalNegotiations).toBe('number');
      expect(response.body.data.summary).toBeDefined();
      expect(typeof response.body.data.summary.totalActiveNegotiations).toBe('number');
      expect(typeof response.body.data.summary.totalInProgressNegotiations).toBe('number');
      expect(typeof response.body.data.summary.totalNegotiations).toBe('number');
    });

    it('should return 400 for missing required parameters', async () => {
      const portfolioId = '30b0e1fb-64c7-4519-93aa-4eb5dd33087e';

      await request(app.getHttpServer())
        .get(`/api/v1/business-base/metrics/negotiations/${portfolioId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });

    it('should return 400 for invalid date format', async () => {
      const portfolioId = '30b0e1fb-64c7-4519-93aa-4eb5dd33087e';

      await request(app.getHttpServer())
        .get(`/api/v1/business-base/metrics/negotiations/${portfolioId}`)
        .query({
          startDate: 'invalid-date',
          endDate: '2024-12-31',
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });

    it('should return 400 for invalid portfolio ID format', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/negotiations/invalid-uuid')
        .query({
          startDate: startDate,
          endDate: endDate,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });

    it('should return 404 for non-existent portfolio', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      const nonExistentPortfolioId = '999e4567-e89b-12d3-a456-************';

      await request(app.getHttpServer())
        .get(`/api/v1/business-base/metrics/negotiations/${nonExistentPortfolioId}`)
        .query({
          startDate: startDate,
          endDate: endDate,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      const portfolioId = '30b0e1fb-64c7-4519-93aa-4eb5dd33087e';

      await request(app.getHttpServer())
        .get(`/api/v1/business-base/metrics/negotiations/${portfolioId}`)
        .query({
          startDate: startDate,
          endDate: endDate,
        })
        .expect(401);
    });
  });
});
